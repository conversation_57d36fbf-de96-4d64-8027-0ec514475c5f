#!/usr/bin/env python3
"""
Test the normalized neural network model on data.csv and compare with data_output.csv
This script loads the trained model, makes predictions, and creates comprehensive comparison plots.
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import pickle
import csv
import os
import sys

# Import the Model class and other necessary components from the main neural network file
try:
    from Full_Neural_Network import Model, load_csv_data
    print("✓ Successfully imported neural network components")
except ImportError as e:
    print(f"Error importing neural network components: {e}")
    print("Make sure Full_Neural_Network.py is in the same directory")
    sys.exit(1)

# We'll use the load_csv_data function from Full_Neural_Network.py

def plot_predictions_comparison(y_actual, y_predicted, feature_names=None, save_path=None):
    """Create comprehensive comparison plots between actual and predicted values"""
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    n_features = y_actual.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i in range(n_features):
        ax = axes[i]
        
        # Scatter plot with perfect prediction line
        ax.scatter(y_actual[:, i], y_predicted[:, i], alpha=0.7, s=50, color='blue', edgecolors='black', linewidth=0.5)
        
        # Perfect prediction line
        min_val = min(y_actual[:, i].min(), y_predicted[:, i].min())
        max_val = max(y_actual[:, i].max(), y_predicted[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        
        # Calculate metrics
        r2 = r2_score(y_actual[:, i], y_predicted[:, i])
        mae = mean_absolute_error(y_actual[:, i], y_predicted[:, i])
        mse = mean_squared_error(y_actual[:, i], y_predicted[:, i])
        rmse = np.sqrt(mse)
        
        # Calculate percentage error
        mean_actual = np.mean(y_actual[:, i])
        percentage_error = (rmse / abs(mean_actual)) * 100
        
        ax.set_xlabel(f'Actual {feature_names[i]}', fontsize=12)
        ax.set_ylabel(f'Predicted {feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}\nMAE = {mae:.6f}, Error = {percentage_error:.2f}%', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add correlation coefficient
        correlation = np.corrcoef(y_actual[:, i], y_predicted[:, i])[0, 1]
        ax.text(0.05, 0.95, f'Correlation: {correlation:.4f}', transform=ax.transAxes, 
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8), fontsize=10)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved as: {save_path}")
    
    plt.show()
    return r2_score(y_actual, y_predicted, multioutput='variance_weighted')

def plot_time_series_comparison(y_actual, y_predicted, feature_names=None, save_path=None):
    """Plot time series comparison showing actual vs predicted over sample index"""
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    n_features = y_actual.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    sample_indices = np.arange(len(y_actual))
    
    for i in range(n_features):
        ax = axes[i]
        
        ax.plot(sample_indices, y_actual[:, i], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(sample_indices, y_predicted[:, i], 'r--', linewidth=2, label='Predicted', alpha=0.8)
        
        ax.set_xlabel('Sample Index', fontsize=12)
        ax.set_ylabel(f'{feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Time Series Comparison', fontsize=12, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Calculate and display RMSE
        rmse = np.sqrt(mean_squared_error(y_actual[:, i], y_predicted[:, i]))
        ax.text(0.02, 0.98, f'RMSE: {rmse:.6f}', transform=ax.transAxes, 
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8), 
                fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Time series comparison plot saved as: {save_path}")
    
    plt.show()

def plot_error_distribution(y_actual, y_predicted, feature_names=None, save_path=None):
    """Plot error distribution histograms for each feature"""
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    errors = y_actual - y_predicted
    n_features = y_actual.shape[1]
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i in range(n_features):
        ax = axes[i]
        
        ax.hist(errors[:, i], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(0, color='red', linestyle='--', linewidth=2, label='Zero Error')
        
        # Calculate statistics
        mean_error = np.mean(errors[:, i])
        std_error = np.std(errors[:, i])
        
        ax.set_xlabel(f'Prediction Error ({feature_names[i]})', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Error Distribution\nMean: {mean_error:.6f}, Std: {std_error:.6f}', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Error distribution plot saved as: {save_path}")
    
    plt.show()

def print_detailed_metrics(y_actual, y_predicted, feature_names=None):
    """Print comprehensive performance metrics"""
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    print("\n" + "="*80)
    print("DETAILED MODEL PERFORMANCE METRICS ON TEST DATA")
    print("="*80)
    
    overall_r2 = r2_score(y_actual, y_predicted, multioutput='variance_weighted')
    print(f"Overall Weighted R² Score: {overall_r2:.4f}")
    print("-"*80)
    
    for i, feature_name in enumerate(feature_names):
        r2 = r2_score(y_actual[:, i], y_predicted[:, i])
        mae = mean_absolute_error(y_actual[:, i], y_predicted[:, i])
        mse = mean_squared_error(y_actual[:, i], y_predicted[:, i])
        rmse = np.sqrt(mse)
        
        # Additional metrics
        mean_actual = np.mean(y_actual[:, i])
        mean_predicted = np.mean(y_predicted[:, i])
        percentage_error = (rmse / abs(mean_actual)) * 100
        correlation = np.corrcoef(y_actual[:, i], y_predicted[:, i])[0, 1]
        
        print(f"{feature_name}:")
        print(f"  R² Score:           {r2:.6f}")
        print(f"  Mean Absolute Error: {mae:.8f}")
        print(f"  Root Mean Sq Error:  {rmse:.8f}")
        print(f"  Percentage Error:    {percentage_error:.3f}%")
        print(f"  Correlation:         {correlation:.6f}")
        print(f"  Mean Actual:         {mean_actual:.6f}")
        print(f"  Mean Predicted:      {mean_predicted:.6f}")
        print(f"  Bias (Pred-Act):     {mean_predicted-mean_actual:.8f}")
        print()

def main():
    """Main function to test the normalized model"""
    print("Neural Network Model Testing on data.csv")
    print("="*60)
    
    # Check if model exists
    model_path = "normalized_model.model"
    if not os.path.exists(model_path):
        print(f"Error: Model file '{model_path}' not found!")
        print("Please run the training script first to create the normalized model.")
        return
    
    # Load the trained model
    print("Loading trained model...")
    try:
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        print("✓ Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Load test data
    print("\nLoading test data...")
    X_test, y_test = load_csv_data('data.csv', 'data_output.csv')
    
    if X_test is None or y_test is None:
        print("Failed to load test data.")
        return
    
    # Make predictions
    print("Making predictions...")
    try:
        y_predicted = model.predict(X_test)
        print("✓ Predictions completed successfully!")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return
    
    # Feature names
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    # Print detailed metrics
    print_detailed_metrics(y_test, y_predicted, feature_names)
    
    # Create comparison plots
    print("\nGenerating comparison plots...")
    
    # 1. Scatter plot comparison
    overall_r2 = plot_predictions_comparison(y_test, y_predicted, feature_names, 
                                           save_path="test_predictions_vs_actual.png")
    
    # 2. Time series comparison
    plot_time_series_comparison(y_test, y_predicted, feature_names, 
                               save_path="test_time_series_comparison.png")
    
    # 3. Error distribution
    plot_error_distribution(y_test, y_predicted, feature_names, 
                           save_path="test_error_distribution.png")
    
    print(f"\n✓ All plots saved successfully!")
    print(f"Overall Model Performance: R² = {overall_r2:.4f}")
    
    # Summary
    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)
    print(f"Test samples: {len(X_test)}")
    print(f"Overall R² Score: {overall_r2:.4f}")
    print("Generated plots:")
    print("  - test_predictions_vs_actual.png")
    print("  - test_time_series_comparison.png") 
    print("  - test_error_distribution.png")

if __name__ == "__main__":
    main()
