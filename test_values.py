import pandas as pd
import numpy as np
import os
import sys

def get_user_input():
    """Get user input for file names and number of rows to extract"""
    print("=== Random Row Extractor for Correlated CSV Files ===\n")
    
    # Get input file name
    input_file = input("Enter the name of your input CSV file (e.g., 'my_input_data.csv'): ").strip()
    if not input_file.endswith('.csv'):
        input_file += '.csv'
    
    # Get output file name
    output_file = input("Enter the name of your output CSV file (e.g., 'my_output_data.csv'): ").strip()
    if not output_file.endswith('.csv'):
        output_file += '.csv'
    
    return input_file, output_file

def detect_separator(file_path):
    """Detect the CSV separator by checking the first few lines"""
    with open(file_path, 'r', encoding='utf-8') as f:
        first_line = f.readline().strip()
        
    # Count common separators
    separators = [';', ',', '\t', '|']
    separator_counts = {sep: first_line.count(sep) for sep in separators}
    
    # Return the separator with the highest count
    best_separator = max(separator_counts, key=separator_counts.get)
    
    if separator_counts[best_separator] > 0:
        return best_separator
    else:
        return ','  # Default to comma

def validate_files(input_file, output_file):
    """Validate that both files exist and have the same number of rows"""
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found!")
        return False, None, None
    
    if not os.path.exists(output_file):
        print(f"Error: File '{output_file}' not found!")
        return False, None, None
    
    try:
        # Detect separators for both files
        input_sep = detect_separator(input_file)
        output_sep = detect_separator(output_file)
        
        print(f"Detected separator for input file: '{input_sep}'")
        print(f"Detected separator for output file: '{output_sep}'")
        
        input_df = pd.read_csv(input_file, sep=input_sep)
        output_df = pd.read_csv(output_file, sep=output_sep)
        
        if len(input_df) != len(output_df):
            print(f"Error: Input file has {len(input_df)} rows but output file has {len(output_df)} rows.")
            print("Both files must have the same number of rows to maintain correlation!")
            return False, None, None
        
        print(f"✓ Both files found and validated!")
        print(f"✓ Input file: {len(input_df)} rows, {len(input_df.columns)} columns")
        print(f"✓ Output file: {len(output_df)} rows, {len(output_df.columns)} columns")
        print(f"✓ Input columns: {list(input_df.columns)}")
        print(f"✓ Output columns: {list(output_df.columns)}")
        return True, input_sep, output_sep
        
    except Exception as e:
        print(f"Error reading files: {e}")
        return False, None, None

def get_num_rows(total_rows):
    """Get the number of rows to extract from user"""
    while True:
        try:
            num_rows = int(input(f"\nHow many rows do you want to randomly extract? (1-{total_rows}): "))
            if 1 <= num_rows <= total_rows:
                return num_rows
            else:
                print(f"Please enter a number between 1 and {total_rows}")
        except ValueError:
            print("Please enter a valid integer")

def extract_random_rows(input_file, output_file, num_rows, input_sep, output_sep):
    """Extract the same random rows from both files and remove them from originals"""
    try:
        # Read both files with correct separators
        print(f"\nReading files...")
        input_df = pd.read_csv(input_file, sep=input_sep)
        output_df = pd.read_csv(output_file, sep=output_sep)
        
        # Generate random indices
        total_rows = len(input_df)
        random_indices = np.random.choice(total_rows, size=num_rows, replace=False)
        random_indices = sorted(random_indices)  # Sort for consistent ordering
        
        print(f"Selected random row indices: {random_indices}")
        
        # Extract the same rows from both files (CUT operation)
        test_input_df = input_df.iloc[random_indices].reset_index(drop=True)
        test_output_df = output_df.iloc[random_indices].reset_index(drop=True)
        
        # Remove the extracted rows from original dataframes (REMOVE operation)
        remaining_input_df = input_df.drop(input_df.index[random_indices]).reset_index(drop=True)
        remaining_output_df = output_df.drop(output_df.index[random_indices]).reset_index(drop=True)
        
        # Save extracted rows to test files
        test_input_file = "test_input.csv"
        test_output_file = "test_output.csv"
        
        test_input_df.to_csv(test_input_file, index=False, sep=input_sep)
        test_output_df.to_csv(test_output_file, index=False, sep=output_sep)
        
        # Update original files with remaining data (PASTE back remaining data)
        remaining_input_df.to_csv(input_file, index=False, sep=input_sep)
        remaining_output_df.to_csv(output_file, index=False, sep=output_sep)
        
        print(f"\n✓ Successfully created test files:")
        print(f"  - {test_input_file} ({len(test_input_df)} rows, {len(test_input_df.columns)} columns)")
        print(f"  - {test_output_file} ({len(test_output_df)} rows, {len(test_output_df.columns)} columns)")
        print(f"\n✓ Updated original files (removed extracted rows):")
        print(f"  - {input_file} ({len(remaining_input_df)} rows remaining, {len(remaining_input_df.columns)} columns)")
        print(f"  - {output_file} ({len(remaining_output_df)} rows remaining, {len(remaining_output_df.columns)} columns)")
        print(f"\nExtracted rows maintain all original columns:")
        print(f"  - Input file columns: {list(test_input_df.columns)}")
        print(f"  - Output file columns: {list(test_output_df.columns)}")
        print(f"\n🎉 Cut and paste operation completed successfully!")
        print(f"📋 {num_rows} rows moved from original files to test files")
        
        return True
        
    except Exception as e:
        print(f"Error during extraction: {e}")
        return False

def main():
    """Main function to run the row extraction process"""
    try:
        # Get user input
        input_file, output_file = get_user_input()
        
        # Validate files
        is_valid, input_sep, output_sep = validate_files(input_file, output_file)
        if not is_valid:
            return
        
        # Get total number of rows
        input_df = pd.read_csv(input_file, sep=input_sep)
        total_rows = len(input_df)
        
        # Get number of rows to extract
        num_rows = get_num_rows(total_rows)
        
        # Set random seed for reproducibility (optional)
        seed = input(f"\nEnter a random seed (press Enter for random): ").strip()
        if seed:
            try:
                np.random.seed(int(seed))
                print(f"Using seed: {seed}")
            except ValueError:
                print("Invalid seed, using random seed")
        
        # Extract random rows
        extract_random_rows(input_file, output_file, num_rows, input_sep, output_sep)
        print("\n🎉 Process completed successfully!")
            
    except KeyboardInterrupt:
        print("\n\nProcess interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")

if __name__ == "__main__":
    main()