#!/usr/bin/env python3
"""
Interactive CSV data cleaning utility
Clean up CSV files by removing empty columns and ensuring proper formatting
"""

import csv
import os
import glob

def list_csv_files():
    """List all CSV files in the current directory"""
    csv_files = glob.glob("*.csv")
    return sorted(csv_files)

def get_user_file_choice(prompt, csv_files):
    """Get user's choice of CSV file"""
    print(f"\n{prompt}")
    print("Available CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")

    while True:
        try:
            choice = input(f"\nEnter your choice (1-{len(csv_files)}) or filename: ").strip()

            # Check if it's a number
            if choice.isdigit():
                idx = int(choice) - 1
                if 0 <= idx < len(csv_files):
                    return csv_files[idx]
                else:
                    print(f"Please enter a number between 1 and {len(csv_files)}")

            # Check if it's a filename
            elif choice in csv_files:
                return choice

            # Check if it's a filename without .csv extension
            elif f"{choice}.csv" in csv_files:
                return f"{choice}.csv"

            else:
                print("Invalid choice. Please try again.")

        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return None
        except Exception as e:
            print(f"Error: {e}. Please try again.")

def clean_input_file(input_filename):
    """Clean the input file by keeping only rows with 7 columns (input data)"""
    input_rows = []

    # Read the original file
    with open(input_filename, 'r', encoding='utf-8') as f:
        reader = csv.reader(f, delimiter=';')
        next(reader)  # Skip header

        for row in reader:
            # Remove empty cells at the end
            row = [cell for cell in row if cell.strip() != '']

            # Only keep rows with exactly 7 columns (input data)
            if len(row) == 7:
                # Check if all values can be converted to float
                try:
                    for val in row:
                        float(val.replace(',', '.'))
                    input_rows.append(row)
                except ValueError:
                    continue  # Skip rows with invalid data

    # Generate output filename
    base_name = os.path.splitext(input_filename)[0]
    output_filename = f"{base_name}_clean.csv"

    # Write cleaned input file
    with open(output_filename, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter=';')
        writer.writerow(['Days_norm', 'M/Mclean', 'Qs/Qt', 'UA', 'Mmax/Mdesignmax', 'UA_norm', 'Q_norm'])
        writer.writerows(input_rows)

    print(f"Cleaned input file: {len(input_rows)} rows with 7 input features -> {output_filename}")
    return len(input_rows), output_filename

def clean_output_file(output_filename):
    """Clean the output file by keeping only rows with 4 columns (output data)"""
    output_rows = []

    # Read the original file
    with open(output_filename, 'r', encoding='utf-8') as f:
        reader = csv.reader(f, delimiter=';')
        next(reader)  # Skip header

        for row in reader:
            # Remove empty cells at the end
            row = [cell for cell in row if cell.strip() != '']

            # Only keep rows with exactly 4 columns (output data)
            if len(row) == 4:
                # Check if all values can be converted to float
                try:
                    for val in row:
                        float(val.replace(',', '.'))
                    output_rows.append(row)
                except ValueError:
                    continue  # Skip rows with invalid data

    # Generate output filename
    base_name = os.path.splitext(output_filename)[0]
    clean_output_filename = f"{base_name}_clean.csv"

    # Write cleaned output file
    with open(clean_output_filename, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter=';')
        writer.writerow(['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm'])
        writer.writerows(output_rows)

    print(f"Cleaned output file: {len(output_rows)} rows with 4 output features -> {clean_output_filename}")
    return len(output_rows), clean_output_filename

def extract_data_from_mixed_file(mixed_filename):
    """Extract input and output data from a mixed CSV file"""
    input_rows = []
    output_rows = []

    # Read the original file
    with open(mixed_filename, 'r', encoding='utf-8') as f:
        reader = csv.reader(f, delimiter=';')
        next(reader)  # Skip header

        for row in reader:
            # Remove empty cells at the end
            row = [cell for cell in row if cell.strip() != '']

            if len(row) == 7:
                # This is input data
                try:
                    for val in row:
                        float(val.replace(',', '.'))
                    input_rows.append(row)
                except ValueError:
                    continue
            elif len(row) == 4:
                # This is output data
                try:
                    for val in row:
                        float(val.replace(',', '.'))
                    output_rows.append(row)
                except ValueError:
                    continue

    # Generate output filenames
    base_name = os.path.splitext(mixed_filename)[0]
    input_clean_filename = f"{base_name}_input_clean.csv"
    output_clean_filename = f"{base_name}_output_clean.csv"

    # Write cleaned input file
    with open(input_clean_filename, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter=';')
        writer.writerow(['Days_norm', 'M/Mclean', 'Qs/Qt', 'UA', 'Mmax/Mdesignmax', 'UA_norm', 'Q_norm'])
        writer.writerows(input_rows)

    # Write cleaned output file
    with open(output_clean_filename, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter=';')
        writer.writerow(['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm'])
        writer.writerows(output_rows)

    print(f"Extracted from mixed file '{mixed_filename}':")
    print(f"  Input data: {len(input_rows)} rows with 7 features -> {input_clean_filename}")
    print(f"  Output data: {len(output_rows)} rows with 4 features -> {output_clean_filename}")

    # Match the number of rows
    min_rows = min(len(input_rows), len(output_rows))
    if len(input_rows) != len(output_rows):
        print(f"Warning: Mismatched row counts. Using first {min_rows} rows from each.")

        # Rewrite files with matching row counts
        with open(input_clean_filename, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter=';')
            writer.writerow(['Days_norm', 'M/Mclean', 'Qs/Qt', 'UA', 'Mmax/Mdesignmax', 'UA_norm', 'Q_norm'])
            writer.writerows(input_rows[:min_rows])

        with open(output_clean_filename, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter=';')
            writer.writerow(['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm'])
            writer.writerows(output_rows[:min_rows])

    return min_rows, input_clean_filename, output_clean_filename

def main():
    print("Interactive CSV Data Cleaning Utility")
    print("="*50)

    # List available CSV files
    csv_files = list_csv_files()
    if not csv_files:
        print("No CSV files found in the current directory.")
        return

    print("Choose cleaning mode:")
    print("1. Clean separate input and output files")
    print("2. Extract input and output data from a mixed file")

    while True:
        try:
            mode = input("\nEnter your choice (1 or 2): ").strip()
            if mode in ['1', '2']:
                break
            else:
                print("Please enter 1 or 2")
        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return

    if mode == '1':
        # Clean separate files
        print("\n" + "="*50)
        input_file = get_user_file_choice("Select the INPUT data file:", csv_files)
        if not input_file:
            return

        output_file = get_user_file_choice("Select the OUTPUT data file:", csv_files)
        if not output_file:
            return

        print(f"\nCleaning separate files...")
        print(f"Input file: {input_file}")
        print(f"Output file: {output_file}")

        # Clean input file
        try:
            input_count, clean_input_filename = clean_input_file(input_file)
        except Exception as e:
            print(f"Error cleaning input file: {e}")
            return

        # Clean output file
        try:
            output_count, clean_output_filename = clean_output_file(output_file)
        except Exception as e:
            print(f"Error cleaning output file: {e}")
            return

        print(f"\n✓ Successfully created clean files:")
        print(f"  - {clean_input_filename} ({input_count} samples)")
        print(f"  - {clean_output_filename} ({output_count} samples)")

        if input_count != output_count:
            print(f"⚠️  Warning: Mismatched sample counts ({input_count} vs {output_count})")

    elif mode == '2':
        # Extract from mixed file
        print("\n" + "="*50)
        mixed_file = get_user_file_choice("Select the MIXED data file (contains both input and output):", csv_files)
        if not mixed_file:
            return

        print(f"\nExtracting data from mixed file: {mixed_file}")

        try:
            num_samples, input_clean_file, output_clean_file = extract_data_from_mixed_file(mixed_file)

            print(f"\n✓ Successfully created clean files with {num_samples} samples each:")
            print(f"  - {input_clean_file} (input data)")
            print(f"  - {output_clean_file} (output data)")
        except Exception as e:
            print(f"Error extracting data from mixed file: {e}")
            return

    # Verify the files
    print("\nVerifying cleaned files...")
    try:
        if mode == '1':
            verify_files = [(clean_input_filename, "Input"), (clean_output_filename, "Output")]
        else:
            verify_files = [(input_clean_file, "Input"), (output_clean_file, "Output")]

        for filename, file_type in verify_files:
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter=';')
                header = next(reader)
                rows = list(reader)
                print(f"{file_type} file '{filename}': {len(rows)} data rows, {len(header)} columns")
                print(f"  Header: {header}")
                if rows:
                    print(f"  First row: {rows[0]}")
                print()
    except Exception as e:
        print(f"Error verifying files: {e}")

if __name__ == "__main__":
    main()
