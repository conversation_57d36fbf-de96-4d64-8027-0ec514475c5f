#!/usr/bin/env python3
"""
Simple test script for the normalized neural network model
Tests on data.csv and compares with data_output.csv
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import os
import sys

# Add the current directory to Python path and import everything from the main neural network
sys.path.append('.')
exec(open('Full_Neural_Network.py').read())

def test_model_on_data():
    """Test the normalized model on data.csv and compare with data_output.csv"""
    print("Neural Network Model Testing on data.csv")
    print("="*60)
    
    # Check if model exists
    model_path = "normalized_model.model"
    if not os.path.exists(model_path):
        print(f"Error: Model file '{model_path}' not found!")
        print("Please run the training script first to create the normalized model.")
        return
    
    # Load the trained model
    print("Loading trained model...")
    try:
        model = Model.load(model_path)
        print("✓ Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Load test data
    print("\nLoading test data...")
    X_test, y_test = load_csv_data('data.csv', 'data_output.csv')
    
    if X_test is None or y_test is None:
        print("Failed to load test data.")
        return
    
    # Make predictions
    print("Making predictions...")
    try:
        y_predicted = model.predict(X_test)
        print("✓ Predictions completed successfully!")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return
    
    # Feature names
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    # Print detailed metrics
    print("\n" + "="*80)
    print("DETAILED MODEL PERFORMANCE METRICS ON TEST DATA")
    print("="*80)
    
    overall_r2 = r2_score(y_test, y_predicted, multioutput='variance_weighted')
    print(f"Overall Weighted R² Score: {overall_r2:.4f}")
    print("-"*80)
    
    for i, feature_name in enumerate(feature_names):
        r2 = r2_score(y_test[:, i], y_predicted[:, i])
        mae = mean_absolute_error(y_test[:, i], y_predicted[:, i])
        mse = mean_squared_error(y_test[:, i], y_predicted[:, i])
        rmse = np.sqrt(mse)
        
        # Additional metrics
        mean_actual = np.mean(y_test[:, i])
        mean_predicted = np.mean(y_predicted[:, i])
        percentage_error = (rmse / abs(mean_actual)) * 100
        correlation = np.corrcoef(y_test[:, i], y_predicted[:, i])[0, 1]
        
        print(f"{feature_name}:")
        print(f"  R² Score:           {r2:.6f}")
        print(f"  Mean Absolute Error: {mae:.8f}")
        print(f"  Root Mean Sq Error:  {rmse:.8f}")
        print(f"  Percentage Error:    {percentage_error:.3f}%")
        print(f"  Correlation:         {correlation:.6f}")
        print(f"  Mean Actual:         {mean_actual:.6f}")
        print(f"  Mean Predicted:      {mean_predicted:.6f}")
        print(f"  Bias (Pred-Act):     {mean_predicted-mean_actual:.8f}")
        print()
    
    # Create comparison plots
    print("Generating comparison plots...")
    
    # 1. Predictions vs Actual scatter plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        # Scatter plot with perfect prediction line
        ax.scatter(y_test[:, i], y_predicted[:, i], alpha=0.7, s=50, color='blue', edgecolors='black', linewidth=0.5)
        
        # Perfect prediction line
        min_val = min(y_test[:, i].min(), y_predicted[:, i].min())
        max_val = max(y_test[:, i].max(), y_predicted[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        
        # Calculate metrics for display
        r2 = r2_score(y_test[:, i], y_predicted[:, i])
        rmse = np.sqrt(mean_squared_error(y_test[:, i], y_predicted[:, i]))
        mae = mean_absolute_error(y_test[:, i], y_predicted[:, i])
        
        ax.set_xlabel(f'Actual {feature_names[i]}', fontsize=12)
        ax.set_ylabel(f'Predicted {feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_predictions_vs_actual.png', dpi=300, bbox_inches='tight')
    print("✓ Predictions vs Actual plot saved as: test_predictions_vs_actual.png")
    plt.show()
    
    # 2. Time series comparison
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    sample_indices = np.arange(len(y_test))
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        ax.plot(sample_indices, y_test[:, i], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(sample_indices, y_predicted[:, i], 'r--', linewidth=2, label='Predicted', alpha=0.8)
        
        ax.set_xlabel('Sample Index', fontsize=12)
        ax.set_ylabel(f'{feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Time Series Comparison', fontsize=12, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Calculate and display RMSE
        rmse = np.sqrt(mean_squared_error(y_test[:, i], y_predicted[:, i]))
        ax.text(0.02, 0.98, f'RMSE: {rmse:.6f}', transform=ax.transAxes, 
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8), 
                fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('test_time_series_comparison.png', dpi=300, bbox_inches='tight')
    print("✓ Time series comparison plot saved as: test_time_series_comparison.png")
    plt.show()
    
    # 3. Error analysis
    errors = y_test - y_predicted
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        ax.hist(errors[:, i], bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(0, color='red', linestyle='--', linewidth=2, label='Zero Error')
        
        # Calculate statistics
        mean_error = np.mean(errors[:, i])
        std_error = np.std(errors[:, i])
        
        ax.set_xlabel(f'Prediction Error ({feature_names[i]})', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Error Distribution\nMean: {mean_error:.6f}, Std: {std_error:.6f}', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_error_distribution.png', dpi=300, bbox_inches='tight')
    print("✓ Error distribution plot saved as: test_error_distribution.png")
    plt.show()
    
    # Print sample predictions
    print("\nSample Predictions (first 10 test samples):")
    print("="*80)
    print("Format: [Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm]")
    for i in range(min(10, len(y_test))):
        print(f"Sample {i+1:2d}:")
        print(f"  Actual:    {y_test[i]}")
        print(f"  Predicted: {y_predicted[i]}")
        print(f"  Error:     {y_test[i] - y_predicted[i]}")
        print()
    
    # Summary
    print("="*80)
    print("TESTING COMPLETE!")
    print("="*80)
    print(f"Test samples: {len(X_test)}")
    print(f"Overall R² Score: {overall_r2:.4f}")
    print("Generated plots:")
    print("  - test_predictions_vs_actual.png")
    print("  - test_time_series_comparison.png") 
    print("  - test_error_distribution.png")
    
    return overall_r2

if __name__ == "__main__":
    test_model_on_data()
