#!/usr/bin/env python3
"""
Standalone script for analyzing and plotting neural network model performance.
This script can load existing models and generate comprehensive analysis plots.
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import pickle
import csv
import os
import sys

def load_csv_data(input_file, output_file):
    """Load input and output data from CSV files with European format"""
    try:
        # Load input data (7 features)
        X = []
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 7:
                    values = []
                    for val in row[:7]:
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    X.append(values)
        
        # Load output data (4 outputs)
        y = []
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 4:
                    values = []
                    for val in row[:4]:
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    y.append(values)
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.float32)
        
        print(f"Loaded {len(X)} samples with {X.shape[1]} inputs and {y.shape[1]} outputs")
        return X, y
        
    except Exception as e:
        print(f"Error loading CSV files: {e}")
        return None, None

def split_data(X, y, validation_split=0.2, random_seed=42):
    """Split data into training and validation sets with fixed seed for reproducibility"""
    np.random.seed(random_seed)
    n_samples = len(X)
    n_validation = int(n_samples * validation_split)
    
    indices = np.random.permutation(n_samples)
    val_indices = indices[:n_validation]
    train_indices = indices[n_validation:]
    
    X_train, X_val = X[train_indices], X[val_indices]
    y_train, y_val = y[train_indices], y[val_indices]
    
    return X_train, y_train, X_val, y_val

def plot_training_history_from_model(model, save_path=None):
    """Plot training history if available in model"""
    if not hasattr(model, 'history') or not model.history['epochs']:
        print("No training history available in this model.")
        return
    
    history = model.history
    epochs = history['epochs']
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Plot loss
    ax1.plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
    if history['val_loss']:
        ax1.plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
    ax1.set_title('Model Loss', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot accuracy
    ax2.plot(epochs, history['train_accuracy'], 'b-', label='Training Accuracy', linewidth=2)
    if history['val_accuracy']:
        ax2.plot(epochs, history['val_accuracy'], 'r-', label='Validation Accuracy', linewidth=2)
    ax2.set_title('Model Accuracy', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training history plot saved as: {save_path}")
    
    plt.show()

def plot_predictions_vs_actual(model, X_test, y_test, feature_names=None, save_path=None):
    """Plot predicted vs actual values for each output feature"""
    predictions = model.predict(X_test)
    
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    n_features = y_test.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.ravel()
    
    for i in range(n_features):
        ax = axes[i]
        
        # Scatter plot
        ax.scatter(y_test[:, i], predictions[:, i], alpha=0.6, s=30)
        
        # Perfect prediction line
        min_val = min(y_test[:, i].min(), predictions[:, i].min())
        max_val = max(y_test[:, i].max(), predictions[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        
        # Calculate metrics
        r2 = r2_score(y_test[:, i], predictions[:, i])
        mae = mean_absolute_error(y_test[:, i], predictions[:, i])
        mse = mean_squared_error(y_test[:, i], predictions[:, i])
        rmse = np.sqrt(mse)
        
        ax.set_xlabel(f'Actual {feature_names[i]}')
        ax.set_ylabel(f'Predicted {feature_names[i]}')
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.3f}, MAE = {mae:.4f}, RMSE = {rmse:.4f}')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Predictions vs actual plot saved as: {save_path}")
    
    plt.show()
    
    return predictions

def plot_residuals(model, X_test, y_test, feature_names=None, save_path=None):
    """Plot residuals (prediction errors) for each output feature"""
    predictions = model.predict(X_test)
    residuals = y_test - predictions
    
    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    n_features = y_test.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.ravel()
    
    for i in range(n_features):
        ax = axes[i]
        
        # Residual plot
        ax.scatter(predictions[:, i], residuals[:, i], alpha=0.6, s=30)
        ax.axhline(y=0, color='r', linestyle='--', linewidth=2)
        
        ax.set_xlabel(f'Predicted {feature_names[i]}')
        ax.set_ylabel(f'Residuals (Actual - Predicted)')
        ax.set_title(f'Residual Plot - {feature_names[i]}')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Residuals plot saved as: {save_path}")
    
    plt.show()

def analyze_model(model_path, input_csv='input.csv', output_csv='output.csv'):
    """Complete analysis of a saved model"""
    print(f"Analyzing model: {model_path}")
    print("=" * 50)
    
    # Load model
    try:
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        print("Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Load data
    print("Loading data...")
    X, y = load_csv_data(input_csv, output_csv)
    if X is None or y is None:
        print("Failed to load data.")
        return
    
    # Split data (using same seed for consistency)
    X_train, y_train, X_val, y_val = split_data(X, y, validation_split=0.2, random_seed=42)
    print(f"Data split: {len(X_train)} training, {len(X_val)} validation samples")
    
    # Model name for saving plots
    model_name = os.path.splitext(os.path.basename(model_path))[0]
    
    # Generate plots
    print("\n1. Training History:")
    plot_training_history_from_model(model, save_path=f"{model_name}_analysis_training_history.png")
    
    print("\n2. Predictions vs Actual (Validation Set):")
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    predictions = plot_predictions_vs_actual(model, X_val, y_val, feature_names, 
                                           save_path=f"{model_name}_analysis_predictions_vs_actual.png")
    
    print("\n3. Residual Analysis:")
    plot_residuals(model, X_val, y_val, feature_names, 
                  save_path=f"{model_name}_analysis_residuals.png")
    
    # Print detailed metrics
    print("\n4. Detailed Performance Metrics:")
    print("=" * 50)
    for i, feature_name in enumerate(feature_names):
        r2 = r2_score(y_val[:, i], predictions[:, i])
        mae = mean_absolute_error(y_val[:, i], predictions[:, i])
        mse = mean_squared_error(y_val[:, i], predictions[:, i])
        rmse = np.sqrt(mse)
        
        print(f"{feature_name}:")
        print(f"  R² Score: {r2:.4f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  MSE: {mse:.6f}")
        print(f"  RMSE: {rmse:.6f}")
        print()

def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python plot_model_analysis.py <model_file.model> [input.csv] [output.csv]")
        print("\nAvailable models:")
        for file in os.listdir('.'):
            if file.endswith('.model'):
                print(f"  {file}")
        return
    
    model_path = sys.argv[1]
    input_csv = sys.argv[2] if len(sys.argv) > 2 else 'input.csv'
    output_csv = sys.argv[3] if len(sys.argv) > 3 else 'output.csv'
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        return
    
    analyze_model(model_path, input_csv, output_csv)

if __name__ == "__main__":
    main()
