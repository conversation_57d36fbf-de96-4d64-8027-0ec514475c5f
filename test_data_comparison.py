#!/usr/bin/env python3
"""
Test the normalized neural network model on data.csv and compare with data_output.csv
This script loads the trained model and creates comprehensive comparison plots.
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler
import pickle
import csv
import os

def load_test_data(input_file, output_file):
    """Load test data from CSV files with European format"""
    try:
        # Load input data (7 features)
        X = []
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 7:
                    values = []
                    for val in row[:7]:
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    X.append(values)
        
        # Load output data (4 outputs)
        y = []
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 4:
                    values = []
                    for val in row[:4]:
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    y.append(values)
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.float32)
        
        print(f"Loaded {len(X)} test samples with {X.shape[1]} inputs and {y.shape[1]} outputs")
        return X, y
        
    except Exception as e:
        print(f"Error loading CSV files: {e}")
        return None, None

def load_model_components(model_path):
    """Load the model and extract the scalers and parameters"""
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        # Extract scalers
        input_scaler = model_data.input_scaler
        output_scaler = model_data.output_scaler
        
        print("✓ Model scalers loaded successfully!")
        return model_data, input_scaler, output_scaler
        
    except Exception as e:
        print(f"Error loading model: {e}")
        return None, None, None

def make_predictions_manual(model, X_test, input_scaler, output_scaler):
    """Make predictions using the loaded model"""
    try:
        # Normalize inputs
        X_normalized = input_scaler.transform(X_test)
        
        # Make predictions (normalized)
        predictions_normalized = model.predict(X_normalized)
        
        # Denormalize predictions
        predictions = output_scaler.inverse_transform(predictions_normalized)
        
        return predictions
        
    except Exception as e:
        print(f"Error making predictions: {e}")
        return None

def create_comparison_plots(y_actual, y_predicted, feature_names):
    """Create comprehensive comparison plots"""
    
    # 1. Predictions vs Actual scatter plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    print("\nDetailed Performance Metrics:")
    print("="*60)
    
    overall_r2_scores = []
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        # Scatter plot
        ax.scatter(y_actual[:, i], y_predicted[:, i], alpha=0.7, s=50, color='blue', edgecolors='black', linewidth=0.5)
        
        # Perfect prediction line
        min_val = min(y_actual[:, i].min(), y_predicted[:, i].min())
        max_val = max(y_actual[:, i].max(), y_predicted[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        
        # Calculate metrics
        r2 = r2_score(y_actual[:, i], y_predicted[:, i])
        mae = mean_absolute_error(y_actual[:, i], y_predicted[:, i])
        rmse = np.sqrt(mean_squared_error(y_actual[:, i], y_predicted[:, i]))
        correlation = np.corrcoef(y_actual[:, i], y_predicted[:, i])[0, 1]
        
        overall_r2_scores.append(r2)
        
        # Print metrics
        print(f"{feature_names[i]}:")
        print(f"  R² Score: {r2:.6f}")
        print(f"  RMSE:     {rmse:.8f}")
        print(f"  MAE:      {mae:.8f}")
        print(f"  Corr:     {correlation:.6f}")
        print()
        
        ax.set_xlabel(f'Actual {feature_names[i]}', fontsize=12)
        ax.set_ylabel(f'Predicted {feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_data_predictions_vs_actual.png', dpi=300, bbox_inches='tight')
    print("✓ Predictions vs Actual plot saved as: test_data_predictions_vs_actual.png")
    plt.show()
    
    # 2. Time series comparison
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    sample_indices = np.arange(len(y_actual))
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        ax.plot(sample_indices, y_actual[:, i], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(sample_indices, y_predicted[:, i], 'r--', linewidth=2, label='Predicted', alpha=0.8)
        
        ax.set_xlabel('Sample Index', fontsize=12)
        ax.set_ylabel(f'{feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Time Series Comparison', fontsize=12, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        rmse = np.sqrt(mean_squared_error(y_actual[:, i], y_predicted[:, i]))
        ax.text(0.02, 0.98, f'RMSE: {rmse:.6f}', transform=ax.transAxes, 
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8), 
                fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('test_data_time_series.png', dpi=300, bbox_inches='tight')
    print("✓ Time series plot saved as: test_data_time_series.png")
    plt.show()
    
    # 3. Error distribution
    errors = y_actual - y_predicted
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        ax = axes[i]
        
        ax.hist(errors[:, i], bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(0, color='red', linestyle='--', linewidth=2, label='Zero Error')
        
        mean_error = np.mean(errors[:, i])
        std_error = np.std(errors[:, i])
        
        ax.set_xlabel(f'Prediction Error ({feature_names[i]})', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Error Distribution\nMean: {mean_error:.6f}, Std: {std_error:.6f}', 
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_data_error_distribution.png', dpi=300, bbox_inches='tight')
    print("✓ Error distribution plot saved as: test_data_error_distribution.png")
    plt.show()
    
    return np.mean(overall_r2_scores)

def main():
    """Main testing function"""
    print("Neural Network Model Testing on data.csv vs data_output.csv")
    print("="*70)
    
    # Check if model exists
    model_path = "normalized_model.model"
    if not os.path.exists(model_path):
        print(f"Error: Model file '{model_path}' not found!")
        print("Please run the training script first to create the normalized model.")
        return
    
    # Load test data
    print("Loading test data...")
    X_test, y_test = load_test_data('data.csv', 'data_output.csv')
    
    if X_test is None or y_test is None:
        print("Failed to load test data.")
        return
    
    # Load model components
    print("Loading model...")
    model, input_scaler, output_scaler = load_model_components(model_path)
    
    if model is None:
        print("Failed to load model.")
        return
    
    # Make predictions using the model's predict method
    print("Making predictions...")
    try:
        y_predicted = model.predict(X_test)
        print("✓ Predictions completed successfully!")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return
    
    # Feature names
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    
    # Create comparison plots and get overall performance
    overall_r2 = create_comparison_plots(y_test, y_predicted, feature_names)
    
    # Print sample comparisons
    print("Sample Predictions (first 5 test samples):")
    print("="*60)
    for i in range(min(5, len(y_test))):
        print(f"Sample {i+1}:")
        print(f"  Actual:    {y_test[i]}")
        print(f"  Predicted: {y_predicted[i]}")
        print(f"  Error:     {y_test[i] - y_predicted[i]}")
        print()
    
    # Final summary
    print("="*70)
    print("TESTING COMPLETE!")
    print("="*70)
    print(f"Test samples: {len(X_test)}")
    print(f"Average R² Score: {overall_r2:.4f}")
    print("Generated plots:")
    print("  - test_data_predictions_vs_actual.png")
    print("  - test_data_time_series.png")
    print("  - test_data_error_distribution.png")

if __name__ == "__main__":
    main()
