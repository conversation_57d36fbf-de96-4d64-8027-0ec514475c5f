import numpy as np
import nnfs
import os
import csv
import pickle
import copy
import glob
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler

nnfs.init()

# Interactive file selection functions
def list_csv_files():
    """List all CSV files in the current directory"""
    csv_files = glob.glob("*.csv")
    return sorted(csv_files)

def get_user_file_choice(prompt, csv_files):
    """Get user's choice of CSV file"""
    print(f"\n{prompt}")
    print("Available CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")

    while True:
        try:
            choice = input(f"\nEnter your choice (1-{len(csv_files)}) or filename: ").strip()

            # Check if it's a number
            if choice.isdigit():
                idx = int(choice) - 1
                if 0 <= idx < len(csv_files):
                    return csv_files[idx]
                else:
                    print(f"Please enter a number between 1 and {len(csv_files)}")

            # Check if it's a filename
            elif choice in csv_files:
                return choice

            # Check if it's a filename without .csv extension
            elif f"{choice}.csv" in csv_files:
                return f"{choice}.csv"

            else:
                print("Invalid choice. Please try again.")

        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return None
        except Exception as e:
            print(f"Error: {e}. Please try again.")

def get_model_name():
    """Get model name from user"""
    while True:
        try:
            model_name = input("\nEnter model name (without .model extension): ").strip()
            if model_name:
                return model_name
            else:
                print("Please enter a valid model name.")
        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return None

# Dense (Fully Connected) Layer Class
# This is the fundamental building block of our neural network
# Each dense layer connects every input to every output with learnable weights and biases
class Layer_Dense:
    def __init__(self, n_inputs, n_neurons,
                 weight_regularizer_l1=0, weight_regularizer_l2=0,
                 bias_regularizer_l1=0, bias_regularizer_l2=0):
        """
        Initialize a dense (fully connected) layer.

        Args:
            n_inputs (int): Number of input features/neurons from previous layer
            n_neurons (int): Number of neurons in this layer
            weight_regularizer_l1 (float): L1 regularization strength for weights (prevents overfitting)
            weight_regularizer_l2 (float): L2 regularization strength for weights (prevents overfitting)
            bias_regularizer_l1 (float): L1 regularization strength for biases
            bias_regularizer_l2 (float): L2 regularization strength for biases
        """
        # Xavier/Glorot initialization for better gradient flow and training stability
        # This initialization helps prevent vanishing/exploding gradients
        self.weights = np.random.randn(n_inputs, n_neurons) * np.sqrt(2.0 / n_inputs)

        # Initialize biases to zero (standard practice)
        # Shape: (1, n_neurons) - one bias per neuron
        self.biases = np.zeros((1, n_neurons))

        # Store regularization parameters (used during training to prevent overfitting)
        self.weight_regularizer_l1 = weight_regularizer_l1  # L1: promotes sparsity
        self.weight_regularizer_l2 = weight_regularizer_l2  # L2: promotes smaller weights
        self.bias_regularizer_l1 = bias_regularizer_l1
        self.bias_regularizer_l2 = bias_regularizer_l2

    def forward(self, inputs, training):
        """
        Forward pass through the dense layer.
        Computes: output = inputs * weights + biases

        Args:
            inputs (numpy.ndarray): Input data from previous layer
            training (bool): Whether in training mode (unused here, kept for consistency)
        """
        # Store inputs for use in backward pass during training
        self.inputs = inputs

        # Compute linear transformation: y = Wx + b
        # inputs shape: (batch_size, n_inputs)
        # weights shape: (n_inputs, n_neurons)
        # output shape: (batch_size, n_neurons)
        self.output = np.dot(inputs, self.weights) + self.biases

    def backward(self, dvalues):
        self.dweights = np.dot(self.inputs.T, dvalues)
        self.dbiases = np.sum(dvalues, axis=0, keepdims=True)

        # Gradients on regularization
        if self.weight_regularizer_l1 > 0:
            dL1 = np.ones_like(self.weights)
            dL1[self.weights < 0] = -1
            self.dweights += self.weight_regularizer_l1 * dL1
        if self.weight_regularizer_l2 > 0:
            self.dweights += 2 * self.weight_regularizer_l2 * self.weights
        if self.bias_regularizer_l1 > 0:
            dL1 = np.ones_like(self.biases)
            dL1[self.biases < 0] = -1
            self.dbiases += self.bias_regularizer_l1 * dL1
        if self.bias_regularizer_l2 > 0:
            self.dbiases += 2 * self.bias_regularizer_l2 * self.biases

        self.dinputs = np.dot(dvalues, self.weights.T)

    def get_parameters(self):
        return self.weights, self.biases

    def set_parameters(self, weights, biases):
        self.weights = weights
        self.biases = biases

# Dropout layer
class Layer_Dropout:
    def __init__(self, rate):
        self.rate = 1 - rate

    def forward(self, inputs, training):
        self.inputs = inputs
        if not training:
            self.output = inputs.copy()
            return
        self.binary_mask = np.random.binomial(1, self.rate, size=inputs.shape) / self.rate
        self.output = inputs * self.binary_mask

    def backward(self, dvalues):
        self.dinputs = dvalues * self.binary_mask

# Input Layer Class
# This is a pass-through layer that simply forwards the input data
# Used as the first layer to standardize the interface
class Layer_Input:
    def forward(self, inputs, training):
        """
        Forward pass for input layer - simply passes through the input data.

        Args:
            inputs (numpy.ndarray): Input data (features)
            training (bool): Whether in training mode (unused here)
        """
        # Simply pass through the input data unchanged
        self.output = inputs

# ReLU (Rectified Linear Unit) Activation Function
# ReLU(x) = max(0, x) - outputs 0 for negative inputs, x for positive inputs
# Most commonly used activation function in hidden layers
class Activation_ReLU:
    def forward(self, inputs, training):
        """
        Forward pass for ReLU activation.
        Applies ReLU function: f(x) = max(0, x)

        Args:
            inputs (numpy.ndarray): Input values from previous layer
            training (bool): Whether in training mode (unused here)
        """
        # Store inputs for backward pass during training
        self.inputs = inputs

        # Apply ReLU: set negative values to 0, keep positive values unchanged
        # This introduces non-linearity and helps the network learn complex patterns
        self.output = np.maximum(0, inputs)

    def backward(self, dvalues):
        self.dinputs = dvalues.copy()
        self.dinputs[self.inputs <= 0] = 0

    def predictions(self, outputs):
        return outputs

# Linear Activation Function (Identity Function)
# f(x) = x - outputs the input unchanged
# Used in the output layer for regression tasks where we want unbounded outputs
class Activation_Linear:
    def forward(self, inputs, training):
        """
        Forward pass for linear activation.
        Applies identity function: f(x) = x (no transformation)

        Args:
            inputs (numpy.ndarray): Input values from previous layer
            training (bool): Whether in training mode (unused here)
        """
        # Store inputs for backward pass during training
        self.inputs = inputs

        # Linear activation: output = input (no transformation)
        # Used in output layer for regression to allow any real-valued output
        self.output = inputs

    def backward(self, dvalues):
        self.dinputs = dvalues.copy()

    def predictions(self, outputs):
        return outputs

# Adam optimizer
class Optimizer_Adam:
    def __init__(self, learning_rate=0.001, decay=0., epsilon=1e-7,
                 beta_1=0.9, beta_2=0.999):
        self.learning_rate = learning_rate
        self.current_learning_rate = learning_rate
        self.decay = decay
        self.iterations = 0
        self.epsilon = epsilon
        self.beta_1 = beta_1
        self.beta_2 = beta_2

    def pre_update_params(self):
        if self.decay:
            self.current_learning_rate = self.learning_rate * \
                (1. / (1. + self.decay * self.iterations))

    def update_params(self, layer):
        if not hasattr(layer, 'weight_cache'):
            layer.weight_momentums = np.zeros_like(layer.weights)
            layer.weight_cache = np.zeros_like(layer.weights)
            layer.bias_momentums = np.zeros_like(layer.biases)
            layer.bias_cache = np.zeros_like(layer.biases)

        layer.weight_momentums = self.beta_1 * layer.weight_momentums + \
                                 (1 - self.beta_1) * layer.dweights
        layer.bias_momentums = self.beta_1 * layer.bias_momentums + \
                               (1 - self.beta_1) * layer.dbiases

        weight_momentums_corrected = layer.weight_momentums / \
            (1 - self.beta_1 ** (self.iterations + 1))
        bias_momentums_corrected = layer.bias_momentums / \
            (1 - self.beta_1 ** (self.iterations + 1))

        layer.weight_cache = self.beta_2 * layer.weight_cache + \
            (1 - self.beta_2) * layer.dweights**2
        layer.bias_cache = self.beta_2 * layer.bias_cache + \
            (1 - self.beta_2) * layer.dbiases**2

        weight_cache_corrected = layer.weight_cache / \
            (1 - self.beta_2 ** (self.iterations + 1))
        bias_cache_corrected = layer.bias_cache / \
            (1 - self.beta_2 ** (self.iterations + 1))

        layer.weights += -self.current_learning_rate * \
                         weight_momentums_corrected / \
                         (np.sqrt(weight_cache_corrected) + self.epsilon)
        layer.biases += -self.current_learning_rate * \
                         bias_momentums_corrected / \
                         (np.sqrt(bias_cache_corrected) + self.epsilon)

    def post_update_params(self):
        self.iterations += 1

# Loss base class
class Loss:
    def regularization_loss(self):
        regularization_loss = 0
        for layer in self.trainable_layers:
            if layer.weight_regularizer_l1 > 0:
                regularization_loss += layer.weight_regularizer_l1 * \
                                       np.sum(np.abs(layer.weights))
            if layer.weight_regularizer_l2 > 0:
                regularization_loss += layer.weight_regularizer_l2 * \
                                       np.sum(layer.weights * layer.weights)
            if layer.bias_regularizer_l1 > 0:
                regularization_loss += layer.bias_regularizer_l1 * \
                                       np.sum(np.abs(layer.biases))
            if layer.bias_regularizer_l2 > 0:
                regularization_loss += layer.bias_regularizer_l2 * \
                                       np.sum(layer.biases * layer.biases)
        return regularization_loss

    def remember_trainable_layers(self, trainable_layers):
        self.trainable_layers = trainable_layers

    def calculate(self, output, y, *, include_regularization=False):
        sample_losses = self.forward(output, y)
        data_loss = np.mean(sample_losses)
        self.accumulated_sum += np.sum(sample_losses)
        self.accumulated_count += len(sample_losses)
        
        if not include_regularization:
            return data_loss
        return data_loss, self.regularization_loss()

    def calculate_accumulated(self, *, include_regularization=False):
        data_loss = self.accumulated_sum / self.accumulated_count
        if not include_regularization:
            return data_loss
        return data_loss, self.regularization_loss()

    def new_pass(self):
        self.accumulated_sum = 0
        self.accumulated_count = 0

# Mean Squared Error loss (for regression)
class Loss_MeanSquaredError(Loss):
    def forward(self, y_pred, y_true):
        sample_losses = np.mean((y_true - y_pred)**2, axis=-1)
        return sample_losses

    def backward(self, dvalues, y_true):
        samples = len(dvalues)
        outputs = len(dvalues[0])
        self.dinputs = -2 * (y_true - dvalues) / outputs
        self.dinputs = self.dinputs / samples

# Regression accuracy class
class Accuracy_Regression:
    def __init__(self):
        self.precision = None

    def init(self, y, reinit=False):
        if self.precision is None or reinit:
            self.precision = np.std(y) / 250

    def calculate(self, predictions, y):
        comparisons = self.compare(predictions, y)
        accuracy = np.mean(comparisons)
        self.accumulated_sum += np.sum(comparisons)
        self.accumulated_count += len(comparisons)
        return accuracy

    def compare(self, predictions, y):
        return np.absolute(predictions - y) < self.precision

    def calculate_accumulated(self):
        accuracy = self.accumulated_sum / self.accumulated_count
        return accuracy

    def new_pass(self):
        self.accumulated_sum = 0
        self.accumulated_count = 0

# Model class
class Model:
    def __init__(self):
        self.layers = []
        # Training history for plotting
        self.history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'epochs': []
        }
        # Data scalers for normalization
        self.input_scaler = None
        self.output_scaler = None

    def add(self, layer):
        self.layers.append(layer)

    def set(self, *, loss=None, optimizer=None, accuracy=None):
        if loss is not None:
            self.loss = loss
        if optimizer is not None:
            self.optimizer = optimizer
        if accuracy is not None:
            self.accuracy = accuracy

    def finalize(self):
        self.input_layer = Layer_Input()
        layer_count = len(self.layers)
        self.trainable_layers = []

        for i in range(layer_count):
            if i == 0:
                self.layers[i].prev = self.input_layer
                self.layers[i].next = self.layers[i+1]
            elif i < layer_count - 1:
                self.layers[i].prev = self.layers[i-1]
                self.layers[i].next = self.layers[i+1]
            else:
                self.layers[i].prev = self.layers[i-1]
                self.layers[i].next = self.loss
                self.output_layer_activation = self.layers[i]

            if hasattr(self.layers[i], 'weights'):
                self.trainable_layers.append(self.layers[i])

        if self.loss is not None:
            self.loss.remember_trainable_layers(self.trainable_layers)

    def fit_scalers(self, X, y):
        """Fit the input and output scalers to the training data"""
        self.input_scaler = StandardScaler()
        self.output_scaler = StandardScaler()

        self.input_scaler.fit(X)
        self.output_scaler.fit(y)

        print("Data scalers fitted successfully!")
        print(f"Input features - Mean: {self.input_scaler.mean_}, Std: {self.input_scaler.scale_}")
        print(f"Output features - Mean: {self.output_scaler.mean_}, Std: {self.output_scaler.scale_}")

    def normalize_inputs(self, X):
        """Normalize input data using fitted scaler"""
        if self.input_scaler is None:
            raise ValueError("Input scaler not fitted. Call fit_scalers() first.")
        return self.input_scaler.transform(X)

    def normalize_outputs(self, y):
        """Normalize output data using fitted scaler"""
        if self.output_scaler is None:
            raise ValueError("Output scaler not fitted. Call fit_scalers() first.")
        return self.output_scaler.transform(y)

    def denormalize_outputs(self, y_normalized):
        """Denormalize output predictions back to original scale"""
        if self.output_scaler is None:
            raise ValueError("Output scaler not fitted. Call fit_scalers() first.")
        return self.output_scaler.inverse_transform(y_normalized)

    def train(self, X, y, *, epochs=1, batch_size=None, print_every=1, validation_data=None):
        # Fit scalers and normalize data
        print("Fitting data scalers and normalizing data...")
        self.fit_scalers(X, y)
        X_normalized = self.normalize_inputs(X)
        y_normalized = self.normalize_outputs(y)

        # Normalize validation data if provided
        if validation_data is not None:
            X_val, y_val = validation_data
            X_val_normalized = self.normalize_inputs(X_val)
            y_val_normalized = self.normalize_outputs(y_val)
            validation_data_normalized = (X_val_normalized, y_val_normalized)
        else:
            validation_data_normalized = None

        self.accuracy.init(y_normalized)
        train_steps = 1

        if batch_size is not None:
            train_steps = len(X_normalized) // batch_size
            if train_steps * batch_size < len(X_normalized):
                train_steps += 1

        for epoch in range(1, epochs+1):
            print(f'epoch: {epoch}')
            self.loss.new_pass()
            self.accuracy.new_pass()

            for step in range(train_steps):
                if batch_size is None:
                    batch_X = X_normalized
                    batch_y = y_normalized
                else:
                    batch_X = X_normalized[step*batch_size:(step+1)*batch_size]
                    batch_y = y_normalized[step*batch_size:(step+1)*batch_size]

                output = self.forward(batch_X, training=True)
                data_loss, regularization_loss = \
                    self.loss.calculate(output, batch_y, include_regularization=True)
                loss = data_loss + regularization_loss

                predictions = self.output_layer_activation.predictions(output)
                accuracy = self.accuracy.calculate(predictions, batch_y)

                self.backward(output, batch_y)

                self.optimizer.pre_update_params()
                for layer in self.trainable_layers:
                    self.optimizer.update_params(layer)
                self.optimizer.post_update_params()

                if not step % print_every or step == train_steps - 1:
                    print(f'step: {step}, ' +
                          f'acc: {accuracy:.3f}, ' +
                          f'loss: {loss:.3f} (' +
                          f'data_loss: {data_loss:.3f}, ' +
                          f'reg_loss: {regularization_loss:.3f}), ' +
                          f'lr: {self.optimizer.current_learning_rate}')

            epoch_data_loss, epoch_regularization_loss = \
                self.loss.calculate_accumulated(include_regularization=True)
            epoch_loss = epoch_data_loss + epoch_regularization_loss
            epoch_accuracy = self.accuracy.calculate_accumulated()

            print(f'training, ' +
                  f'acc: {epoch_accuracy:.3f}, ' +
                  f'loss: {epoch_loss:.3f} (' +
                  f'data_loss: {epoch_data_loss:.3f}, ' +
                  f'reg_loss: {epoch_regularization_loss:.3f}), ' +
                  f'lr: {self.optimizer.current_learning_rate}')

            # Record training history
            self.history['epochs'].append(epoch)
            self.history['train_loss'].append(epoch_loss)
            self.history['train_accuracy'].append(epoch_accuracy)

            if validation_data is not None:
                # Use normalized validation data for evaluation
                val_loss, val_accuracy = self.evaluate(X_val_normalized, y_val_normalized, batch_size=batch_size, return_metrics=True)
                self.history['val_loss'].append(val_loss)
                self.history['val_accuracy'].append(val_accuracy)

    def evaluate(self, X_val, y_val, *, batch_size=None, return_metrics=False):
        validation_steps = 1
        if batch_size is not None:
            validation_steps = len(X_val) // batch_size
            if validation_steps * batch_size < len(X_val):
                validation_steps += 1

        self.loss.new_pass()
        self.accuracy.new_pass()

        for step in range(validation_steps):
            if batch_size is None:
                batch_X = X_val
                batch_y = y_val
            else:
                batch_X = X_val[step*batch_size:(step+1)*batch_size]
                batch_y = y_val[step*batch_size:(step+1)*batch_size]

            output = self.forward(batch_X, training=False)
            self.loss.calculate(output, batch_y)
            predictions = self.output_layer_activation.predictions(output)
            self.accuracy.calculate(predictions, batch_y)

        validation_loss = self.loss.calculate_accumulated()
        validation_accuracy = self.accuracy.calculate_accumulated()
        print(f'validation, acc: {validation_accuracy:.3f}, loss: {validation_loss:.3f}')

        if return_metrics:
            return validation_loss, validation_accuracy

    def predict(self, X, *, batch_size=None):
        # Normalize input data
        X_normalized = self.normalize_inputs(X)

        prediction_steps = 1
        if batch_size is not None:
            prediction_steps = len(X_normalized) // batch_size
            if prediction_steps * batch_size < len(X_normalized):
                prediction_steps += 1

        output = []
        for step in range(prediction_steps):
            if batch_size is None:
                batch_X = X_normalized
            else:
                batch_X = X_normalized[step*batch_size:(step+1)*batch_size]
            batch_output = self.forward(batch_X, training=False)
            output.append(batch_output)

        # Denormalize predictions back to original scale
        predictions_normalized = np.vstack(output)
        predictions = self.denormalize_outputs(predictions_normalized)
        return predictions

    def forward(self, X, training):
        self.input_layer.forward(X, training)
        for layer in self.layers:
            layer.forward(layer.prev.output, training)
        return layer.output

    def backward(self, output, y):
        self.loss.backward(output, y)
        for layer in reversed(self.layers):
            layer.backward(layer.next.dinputs)

    def get_parameters(self):
        parameters = []
        for layer in self.trainable_layers:
            parameters.append(layer.get_parameters())
        return parameters

    def set_parameters(self, parameters):
        for parameter_set, layer in zip(parameters, self.trainable_layers):
            layer.set_parameters(*parameter_set)

    def save_parameters(self, path):
        with open(path, 'wb') as f:
            pickle.dump(self.get_parameters(), f)

    def load_parameters(self, path):
        with open(path, 'rb') as f:
            self.set_parameters(pickle.load(f))

    def save(self, path):
        model = copy.deepcopy(self)
        model.loss.new_pass()
        model.accuracy.new_pass()
        model.input_layer.__dict__.pop('output', None)
        model.loss.__dict__.pop('dinputs', None)

        for layer in model.layers:
            for property in ['inputs', 'output', 'dinputs', 'dweights', 'dbiases']:
                layer.__dict__.pop(property, None)

        with open(path, 'wb') as f:
            pickle.dump(model, f)

    @staticmethod
    def load(path):
        with open(path, 'rb') as f:
            model = pickle.load(f)
        return model

# Data loading functions
def load_csv_data(input_file, output_file):
    """Load input and output data from CSV files with European format (semicolon delimiter, comma decimal separator)"""
    try:
        # Load input data (7 features)
        X = []
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 7:
                    # Convert European decimal format (comma) to standard format (period)
                    values = []
                    for val in row[:7]:
                        # Replace comma with period for decimal separator
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    X.append(values)

        # Load output data (4 outputs)
        y = []
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 4:
                    # Convert European decimal format (comma) to standard format (period)
                    values = []
                    for val in row[:4]:
                        # Replace comma with period for decimal separator
                        val_clean = val.replace(',', '.')
                        values.append(float(val_clean))
                    y.append(values)

        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.float32)

        if len(X) != len(y):
            print(f"Warning: Input samples ({len(X)}) and output samples ({len(y)}) don't match!")
            min_samples = min(len(X), len(y))
            X = X[:min_samples]
            y = y[:min_samples]

        print(f"Loaded {len(X)} samples with {X.shape[1]} inputs and {y.shape[1]} outputs")
        print(f"Input features: Days_norm, M/Mclean, Qs/Qt, UA, Mmax/Mdesignmax, UA_norm, Q_norm")
        print(f"Output features: Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm")
        return X, y

    except Exception as e:
        print(f"Error loading CSV files: {e}")
        print("Make sure the files use semicolon (;) as delimiter and comma (,) as decimal separator")
        return None, None

def split_data(X, y, validation_split=0.2):
    """Split data into training and validation sets"""
    n_samples = len(X)
    n_validation = int(n_samples * validation_split)

    # Shuffle indices
    indices = np.random.permutation(n_samples)

    val_indices = indices[:n_validation]
    train_indices = indices[n_validation:]

    X_train, X_val = X[train_indices], X[val_indices]
    y_train, y_val = y[train_indices], y[val_indices]

    return X_train, y_train, X_val, y_val

def plot_training_history(model, save_path=None):
    """Plot training and validation loss and accuracy"""
    history = model.history
    epochs = history['epochs']

    if not epochs:
        print("No training history available for plotting.")
        return

    # Create subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # Plot loss
    ax1.plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
    if history['val_loss']:
        ax1.plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
    ax1.set_title('Model Loss', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot accuracy
    ax2.plot(epochs, history['train_accuracy'], 'b-', label='Training Accuracy', linewidth=2)
    if history['val_accuracy']:
        ax2.plot(epochs, history['val_accuracy'], 'r-', label='Validation Accuracy', linewidth=2)
    ax2.set_title('Model Accuracy', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training history plot saved as: {save_path}")

    plt.show()

def plot_predictions_vs_actual(model, X_test, y_test, feature_names=None, save_path=None):
    """Plot predicted vs actual values for each output feature"""
    predictions = model.predict(X_test)

    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']

    n_features = y_test.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.ravel()

    for i in range(n_features):
        ax = axes[i]

        # Scatter plot
        ax.scatter(y_test[:, i], predictions[:, i], alpha=0.6, s=30)

        # Perfect prediction line
        min_val = min(y_test[:, i].min(), predictions[:, i].min())
        max_val = max(y_test[:, i].max(), predictions[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')

        # Calculate metrics
        r2 = r2_score(y_test[:, i], predictions[:, i])
        mae = mean_absolute_error(y_test[:, i], predictions[:, i])
        mse = mean_squared_error(y_test[:, i], predictions[:, i])
        rmse = np.sqrt(mse)

        ax.set_xlabel(f'Actual {feature_names[i]}')
        ax.set_ylabel(f'Predicted {feature_names[i]}')
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.3f}, MAE = {mae:.4f}, RMSE = {rmse:.4f}')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Predictions vs actual plot saved as: {save_path}")

    plt.show()

    return predictions

def plot_residuals(model, X_test, y_test, feature_names=None, save_path=None):
    """Plot residuals (prediction errors) for each output feature"""
    predictions = model.predict(X_test)
    residuals = y_test - predictions

    if feature_names is None:
        feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']

    n_features = y_test.shape[1]
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.ravel()

    for i in range(n_features):
        ax = axes[i]

        # Residual plot
        ax.scatter(predictions[:, i], residuals[:, i], alpha=0.6, s=30)
        ax.axhline(y=0, color='r', linestyle='--', linewidth=2)

        ax.set_xlabel(f'Predicted {feature_names[i]}')
        ax.set_ylabel(f'Residuals (Actual - Predicted)')
        ax.set_title(f'Residual Plot - {feature_names[i]}')
        ax.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Residuals plot saved as: {save_path}")

    plt.show()

def get_existing_models():
    """Get list of existing model files"""
    models = []
    for file in os.listdir('.'):
        if file.endswith('.model'):
            models.append(file[:-6])  # Remove .model extension
    return models

def create_heat_exchanger_model():
    """Create a neural network model for heat exchanger prediction with improved architecture"""
    model = Model()

    # Input layer: 7 inputs (Days_norm, M/Mclean, Qs/Qt, UA, Mmax/Mdesignmax, UA_norm, Q_norm)
    # Hidden layer 1: 64 neurons with ReLU activation (reduced from 128 to prevent overfitting)
    model.add(Layer_Dense(7, 64, weight_regularizer_l2=1e-4))
    model.add(Activation_ReLU())
    model.add(Layer_Dropout(0.3))

    # Hidden layer 2: 32 neurons with ReLU activation
    model.add(Layer_Dense(64, 32, weight_regularizer_l2=1e-4))
    model.add(Activation_ReLU())
    model.add(Layer_Dropout(0.2))

    # Hidden layer 3: 16 neurons with ReLU activation
    model.add(Layer_Dense(32, 16, weight_regularizer_l2=1e-4))
    model.add(Activation_ReLU())
    model.add(Layer_Dropout(0.1))

    # Output layer: 4 outputs (Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm)
    model.add(Layer_Dense(16, 4))
    model.add(Activation_Linear())

    # Set loss function, optimizer, and accuracy metric with higher learning rate for normalized data
    model.set(
        loss=Loss_MeanSquaredError(),
        optimizer=Optimizer_Adam(learning_rate=0.01, decay=1e-6),  # Higher learning rate for normalized data
        accuracy=Accuracy_Regression()
    )

    model.finalize()
    return model

# Main execution
def main():
    print("Interactive Heat Exchanger Neural Network")
    print("=" * 60)

    # List available CSV files
    csv_files = list_csv_files()
    if not csv_files:
        print("No CSV files found in the current directory.")
        return

    # Get input and output files from user
    print("\nSelect training data files:")
    input_file = get_user_file_choice("Select the INPUT data file:", csv_files)
    if not input_file:
        return

    output_file = get_user_file_choice("Select the OUTPUT data file:", csv_files)
    if not output_file:
        return

    # Load data
    print(f"\nLoading data from CSV files...")
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    X, y = load_csv_data(input_file, output_file)

    if X is None or y is None:
        print(f"Failed to load data. Please ensure {input_file} and {output_file} exist and are properly formatted.")
        return
    
    # Split data
    X_train, y_train, X_val, y_val = split_data(X, y, validation_split=0.2)
    print(f"Training samples: {len(X_train)}, Validation samples: {len(X_val)}")
    
    # Get user choice
    existing_models = get_existing_models()
    
    if existing_models:
        print(f"\nFound existing models: {', '.join(existing_models)}")
        choice = input("Train existing model or create new? (existing/new): ").strip().lower()
    else:
        print("\nNo existing models found.")
        choice = "new"
    
    if choice == "existing" and existing_models:
        print("\nExisting models:")
        for i, model_name in enumerate(existing_models, 1):
            print(f"{i}. {model_name}")
        
        try:
            model_choice = int(input("Select model number: ")) - 1
            if 0 <= model_choice < len(existing_models):
                model_name = existing_models[model_choice]
                print(f"Loading model: {model_name}")
                model = Model.load(f"{model_name}.model")
            else:
                print("Invalid choice. Creating new model.")
                model_name = input("Enter name for new model: ").strip()
                model = create_heat_exchanger_model()
        except (ValueError, IndexError):
            print("Invalid input. Creating new model.")
            model_name = input("Enter name for new model: ").strip()
            model = create_heat_exchanger_model()
    else:
        model_name = input("Enter name for new model: ").strip()
        print("Creating new heat exchanger model...")
        model = create_heat_exchanger_model()
    
    # Training parameters
    epochs = int(input("Enter number of epochs (default 100): ") or "100")
    batch_size = int(input("Enter batch size (default 32): ") or "32")
    
    print(f"\nStarting training for {epochs} epochs...")
    print("Model architecture:")
    print("Input: 7 features (Days_norm, M/Mclean, Qs/Qt, UA, Mmax/Mdesignmax, UA_norm, Q_norm)")
    print("Hidden layers: 64 -> 32 -> 16 neurons (ReLU + Dropout)")
    print("Output: 4 values (Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm)")
    print("Loss: Mean Squared Error")
    print("Optimizer: Adam (LR=0.01)")
    print("Data: Normalized using StandardScaler")
    print("-" * 50)
    
    # Train the model
    model.train(
        X_train, y_train,
        epochs=epochs,
        batch_size=batch_size,
        print_every=10,
        validation_data=(X_val, y_val)
    )
    
    # Save the trained model
    model.save(f"{model_name}.model")
    print(f"\nModel saved as {model_name}.model")
    
    # Final evaluation
    print("\nFinal evaluation on validation set:")
    model.evaluate(X_val, y_val)

    # Plot training history
    print("\nGenerating training history plots...")
    plot_training_history(model, save_path=f"{model_name}_training_history.png")

    # Plot predictions vs actual values
    print("\nGenerating prediction analysis plots...")
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']
    predictions = plot_predictions_vs_actual(model, X_val, y_val, feature_names,
                                           save_path=f"{model_name}_predictions_vs_actual.png")

    # Plot residuals
    print("\nGenerating residual plots...")
    plot_residuals(model, X_val, y_val, feature_names,
                  save_path=f"{model_name}_residuals.png")

    # Make sample predictions
    print("\nSample predictions on validation set:")
    print("Actual vs Predicted (first 5 validation samples):")
    print("Format: [Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm]")
    for i in range(min(5, len(y_val))):
        print(f"Actual:    {y_val[i]}")
        print(f"Predicted: {predictions[i]}")
        print()

    # Calculate and display overall metrics
    print("\nOverall Model Performance Metrics:")
    print("=" * 50)
    for i, feature_name in enumerate(feature_names):
        r2 = r2_score(y_val[:, i], predictions[:, i])
        mae = mean_absolute_error(y_val[:, i], predictions[:, i])
        mse = mean_squared_error(y_val[:, i], predictions[:, i])
        rmse = np.sqrt(mse)

        print(f"{feature_name}:")
        print(f"  R² Score: {r2:.4f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  MSE: {mse:.6f}")
        print(f"  RMSE: {rmse:.6f}")
        print()

    print("All plots have been saved as PNG files in the current directory.")

def predict_only_mode(model_name, test_input_file):
    """
    Make predictions on input data without comparison to actual values.
    This mode is used when you only have input data and want to see what the model predicts.

    Args:
        model_name (str): Name of the trained model file (without .model extension)
        test_input_file (str): Path to CSV file containing input data

    Returns:
        numpy.ndarray: Array of predictions if successful, None if failed
    """
    print(f"Making predictions with model '{model_name}' on input data")
    print("="*60)

    # Load the trained neural network model from disk
    # The model file contains the trained weights, biases, and data scalers
    model_path = f"{model_name}.model"
    if not os.path.exists(model_path):
        print(f"Error: Model file '{model_path}' not found!")
        return

    try:
        # Load the complete model including architecture, weights, and data scalers
        model = Model.load(model_path)
        print("✓ Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return

    # Load input data only (no output/target values needed for prediction-only mode)
    print(f"Loading input data from {test_input_file}...")
    try:
        X_test = []  # List to store input samples

        # Open CSV file with European format (semicolon delimiter, comma decimal separator)
        with open(test_input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            next(reader)  # Skip the header row

            # Process each row in the CSV file
            for row in reader:
                # Remove empty cells and whitespace from the row
                row = [cell.strip() for cell in row if cell.strip()]

                # Check if row has exactly 7 columns (expected input features)
                if len(row) == 7:  # Expected: Days_norm, M/Mclean, Qs/Qt, UA, Mmax/Mdesignmax, UA_norm, Q_norm
                    try:
                        # Convert European decimal format (comma) to standard format (dot) and convert to float
                        row_floats = [float(val.replace(',', '.')) for val in row]
                        X_test.append(row_floats)
                    except ValueError:
                        # Skip rows with invalid numeric data
                        continue

        # Convert list to numpy array for neural network processing
        X_test = np.array(X_test)
        print(f"Loaded {len(X_test)} samples with {X_test.shape[1]} input features")

        # Check if any valid data was loaded
        if len(X_test) == 0:
            print("No valid input data found!")
            return

    except Exception as e:
        print(f"Error loading input data: {e}")
        return

    # Make predictions using the trained neural network model
    print("Making predictions...")
    try:
        # The model.predict() method:
        # 1. Normalizes input data using the stored input scaler
        # 2. Passes data through the neural network layers
        # 3. Denormalizes output predictions using the stored output scaler
        # 4. Returns predictions in original scale
        y_predicted = model.predict(X_test)
        print("✓ Predictions completed successfully!")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return

    # Define feature names for clear labeling and documentation
    # Input features: 7 normalized engineering parameters
    input_feature_names = ['Days_norm', 'M/Mclean', 'Qs/Qt', 'UA', 'Mmax/Mdesignmax', 'UA_norm', 'Q_norm']
    # Output features: 4 predicted heat exchanger performance metrics
    output_feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']

    # Display prediction statistics
    print("\n" + "="*80)
    print("PREDICTION STATISTICS")
    print("="*80)
    print(f"Number of predictions: {len(y_predicted)}")
    print(f"Input features: {', '.join(input_feature_names)}")
    print(f"Output features: {', '.join(output_feature_names)}")
    print()

    for i, feature_name in enumerate(output_feature_names):
        values = y_predicted[:, i]
        print(f"{feature_name}:")
        print(f"  Mean: {np.mean(values):.6f}")
        print(f"  Std:  {np.std(values):.6f}")
        print(f"  Min:  {np.min(values):.6f}")
        print(f"  Max:  {np.max(values):.6f}")
        print()

    # Create comprehensive prediction visualization plots
    print("Generating prediction visualization plots...")

    # PLOT 1: Prediction distribution histograms
    # This shows the distribution of predicted values for each output feature
    # Helps understand the range and spread of predictions
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()  # Flatten 2D array to 1D for easier indexing

    for i in range(len(output_feature_names)):
        ax = axes[i]
        values = y_predicted[:, i]  # Get predictions for this feature

        # Create histogram showing distribution of predicted values
        ax.hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax.set_title(f'{output_feature_names[i]} - Prediction Distribution', fontsize=14, fontweight='bold')
        ax.set_xlabel('Predicted Value', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.grid(True, alpha=0.3)

        # Add statistical summary as text box
        stats_text = f'Mean: {np.mean(values):.4f}\nStd: {np.std(values):.4f}\nMin: {np.min(values):.4f}\nMax: {np.max(values):.4f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8), fontsize=10)

    plt.tight_layout()
    prediction_dist_filename = f"{model_name}_prediction_distributions.png"
    plt.savefig(prediction_dist_filename, dpi=300, bbox_inches='tight')
    plt.close()  # Close figure to free memory

    # PLOT 2: Time series of predictions
    # This shows how predictions vary across different input samples
    # Useful for identifying patterns or trends in the predictions
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()  # Flatten for easier indexing

    sample_indices = np.arange(len(y_predicted))  # Create x-axis indices (0, 1, 2, ...)

    for i in range(len(output_feature_names)):
        ax = axes[i]
        # Plot predictions as a line graph over sample indices
        ax.plot(sample_indices, y_predicted[:, i], 'b-', linewidth=1.5, alpha=0.8, label='Predictions')
        ax.set_title(f'{output_feature_names[i]} - Prediction Time Series', fontsize=14, fontweight='bold')
        ax.set_xlabel('Sample Index', fontsize=12)
        ax.set_ylabel('Predicted Value', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)

        # Add horizontal line showing mean prediction
        mean_val = np.mean(y_predicted[:, i])
        ax.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.4f}')
        ax.legend(fontsize=10)

    plt.tight_layout()
    prediction_series_filename = f"{model_name}_prediction_time_series.png"
    plt.savefig(prediction_series_filename, dpi=300, bbox_inches='tight')
    plt.close()  # Close figure to free memory

    # PLOT 3: Comprehensive prediction values visualization
    # This shows all predicted values in a single comprehensive view
    # Useful for seeing the overall pattern and relationships between features
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))

    # Create a color map based on sample index for better visualization
    colors = plt.cm.viridis(np.linspace(0, 1, len(y_predicted)))

    # Plot 1: Q_average vs Temp_Out_Shell
    scatter1 = ax1.scatter(y_predicted[:, 0], y_predicted[:, 1], c=colors, alpha=0.6, s=30)
    ax1.set_xlabel('Q_average (Predicted)', fontsize=12)
    ax1.set_ylabel('Temp_Out_Shell (Predicted)', fontsize=12)
    ax1.set_title('Q_average vs Temp_Out_Shell Predictions', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Q_average vs Temp_Out_Tube
    scatter2 = ax2.scatter(y_predicted[:, 0], y_predicted[:, 2], c=colors, alpha=0.6, s=30)
    ax2.set_xlabel('Q_average (Predicted)', fontsize=12)
    ax2.set_ylabel('Temp_Out_Tube (Predicted)', fontsize=12)
    ax2.set_title('Q_average vs Temp_Out_Tube Predictions', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)

    # Plot 3: Q_average vs R_norm
    scatter3 = ax3.scatter(y_predicted[:, 0], y_predicted[:, 3], c=colors, alpha=0.6, s=30)
    ax3.set_xlabel('Q_average (Predicted)', fontsize=12)
    ax3.set_ylabel('R_norm (Predicted)', fontsize=12)
    ax3.set_title('Q_average vs R_norm Predictions', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)

    # Plot 4: All predictions normalized and overlaid
    # Normalize each feature to 0-1 range for comparison
    y_normalized = (y_predicted - y_predicted.min(axis=0)) / (y_predicted.max(axis=0) - y_predicted.min(axis=0))

    for i, feature_name in enumerate(output_feature_names):
        ax4.plot(sample_indices, y_normalized[:, i], label=feature_name, linewidth=2, alpha=0.8)

    ax4.set_xlabel('Sample Index', fontsize=12)
    ax4.set_ylabel('Normalized Predicted Value (0-1)', fontsize=12)
    ax4.set_title('All Predictions (Normalized)', fontsize=14, fontweight='bold')
    ax4.legend(fontsize=10)
    ax4.grid(True, alpha=0.3)

    # Add colorbar to show sample progression
    cbar = plt.colorbar(scatter1, ax=[ax1, ax2, ax3], orientation='horizontal', pad=0.1, shrink=0.8)
    cbar.set_label('Sample Index', fontsize=12)

    #plt.tight_layout()
    #prediction_values_filename = f"{model_name}_prediction_values.png"
    #plt.savefig(prediction_values_filename, dpi=300, bbox_inches='tight')
    #plt.close()  # Close figure to free memory

    # Display detailed sample predictions with enhanced formatting
    print("Sample predictions (first 10 samples):")
    print("="*100)
    print("Format: [Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm]")
    print("-"*100)

    # Header for better readability
    print(f"{'Sample':<8} {'Q_average':<12} {'Temp_Out_Shell':<15} {'Temp_Out_Tube':<15} {'R_norm':<12}")
    print("-"*100)

    # Display first 10 predictions with improved formatting
    for i in range(min(10, len(y_predicted))):
        print(f"{i+1:<8} {y_predicted[i, 0]:<12.6f} {y_predicted[i, 1]:<15.6f} {y_predicted[i, 2]:<15.6f} {y_predicted[i, 3]:<12.6f}")

    if len(y_predicted) > 10:
        print(f"... and {len(y_predicted) - 10} more samples")

    # Final summary and completion message
    print("\n" + "="*100)
    print("PREDICTION-ONLY MODE COMPLETE!")
    print("="*100)
    print(f"✓ Successfully processed {len(X_test)} input samples")
    print(f"✓ Generated {len(y_predicted)} predictions for {len(output_feature_names)} output features")
    print(f"✓ Created comprehensive visualization plots")
    print()
    print("Generated plot files:")
    print(f"  📊 {prediction_dist_filename} - Distribution histograms of predicted values")
    print(f"  📈 {prediction_series_filename} - Time series plots of predictions")
    #print(f"  🔍 {prediction_values_filename} - Comprehensive prediction relationships")
    print()
    print("These plots help you understand:")
    print("  • Distribution and spread of predicted values")
    print("  • Patterns and trends in predictions across samples")
    print("  • Relationships between different output features")
    print("  • Overall model behavior on your input data")

    # Return the predictions array for potential further use
    return y_predicted

def test_model_on_external_data(model_name, test_input_file, test_output_file):
    """Test a trained model on external data files and create comparison plots"""
    print(f"Testing model '{model_name}' on external data with comparison")
    print("="*60)

    # Load the trained model
    model_path = f"{model_name}.model"
    if not os.path.exists(model_path):
        print(f"Error: Model file '{model_path}' not found!")
        return

    try:
        model = Model.load(model_path)
        print("✓ Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return

    # Load test data
    print(f"Loading test data from {test_input_file} and {test_output_file}...")
    X_test, y_test = load_csv_data(test_input_file, test_output_file)

    if X_test is None or y_test is None:
        print("Failed to load test data.")
        return

    # Make predictions
    print("Making predictions...")
    try:
        y_predicted = model.predict(X_test)
        print("✓ Predictions completed successfully!")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return

    # Feature names
    feature_names = ['Q_average', 'Temp_Out_Shell', 'Temp_Out_Tube', 'R_norm']

    # Calculate and print detailed metrics
    print("\n" + "="*80)
    print("DETAILED MODEL PERFORMANCE METRICS ON TEST DATA")
    print("="*80)

    from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error

    overall_r2 = r2_score(y_test, y_predicted, multioutput='variance_weighted')
    print(f"Overall Weighted R² Score: {overall_r2:.4f}")
    print("-"*80)

    for i, feature_name in enumerate(feature_names):
        r2 = r2_score(y_test[:, i], y_predicted[:, i])
        mae = mean_absolute_error(y_test[:, i], y_predicted[:, i])
        mse = mean_squared_error(y_test[:, i], y_predicted[:, i])
        rmse = np.sqrt(mse)

        # Additional metrics
        mean_actual = np.mean(y_test[:, i])
        mean_predicted = np.mean(y_predicted[:, i])
        percentage_error = (rmse / abs(mean_actual)) * 100
        correlation = np.corrcoef(y_test[:, i], y_predicted[:, i])[0, 1]

        print(f"{feature_name}:")
        print(f"  R² Score:           {r2:.6f}")
        print(f"  Mean Absolute Error: {mae:.8f}")
        print(f"  Root Mean Sq Error:  {rmse:.8f}")
        print(f"  Percentage Error:    {percentage_error:.3f}%")
        print(f"  Correlation:         {correlation:.6f}")
        print(f"  Mean Actual:         {mean_actual:.6f}")
        print(f"  Mean Predicted:      {mean_predicted:.6f}")
        print(f"  Bias (Pred-Act):     {mean_predicted-mean_actual:.8f}")
        print()

    # Create comparison plots
    print("Generating comparison plots...")

    # 1. Predictions vs Actual scatter plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()

    for i in range(len(feature_names)):
        ax = axes[i]

        # Scatter plot
        ax.scatter(y_test[:, i], y_predicted[:, i], alpha=0.7, s=50, color='blue', edgecolors='black', linewidth=0.5)

        # Perfect prediction line
        min_val = min(y_test[:, i].min(), y_predicted[:, i].min())
        max_val = max(y_test[:, i].max(), y_predicted[:, i].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')

        # Calculate metrics for display
        r2 = r2_score(y_test[:, i], y_predicted[:, i])
        rmse = np.sqrt(mean_squared_error(y_test[:, i], y_predicted[:, i]))

        ax.set_xlabel(f'Actual {feature_names[i]}', fontsize=12)
        ax.set_ylabel(f'Predicted {feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}',
                    fontsize=11, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plot_name = f"{model_name}_test_predictions_vs_actual.png"
    plt.savefig(plot_name, dpi=300, bbox_inches='tight')
    print(f"✓ Predictions vs Actual plot saved as: {plot_name}")
    plt.show()

    # 2. Time series comparison
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()

    sample_indices = np.arange(len(y_test))

    for i in range(len(feature_names)):
        ax = axes[i]

        ax.plot(sample_indices, y_test[:, i], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(sample_indices, y_predicted[:, i], 'r--', linewidth=2, label='Predicted', alpha=0.8)

        ax.set_xlabel('Sample Index', fontsize=12)
        ax.set_ylabel(f'{feature_names[i]}', fontsize=12)
        ax.set_title(f'{feature_names[i]} - Time Series Comparison', fontsize=12, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)

        rmse = np.sqrt(mean_squared_error(y_test[:, i], y_predicted[:, i]))
        ax.text(0.02, 0.98, f'RMSE: {rmse:.6f}', transform=ax.transAxes,
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                fontsize=10, verticalalignment='top')

    plt.tight_layout()
    plot_name = f"{model_name}_test_time_series.png"
    plt.savefig(plot_name, dpi=300, bbox_inches='tight')
    print(f"✓ Time series plot saved as: {plot_name}")
    plt.show()

    # Print sample predictions
    print("\nSample Predictions (first 5 test samples):")
    print("="*80)
    print("Format: [Q_average, Temp_Out_Shell, Temp_Out_Tube, R_norm]")
    for i in range(min(5, len(y_test))):
        print(f"Sample {i+1:2d}:")
        print(f"  Actual:    {y_test[i]}")
        print(f"  Predicted: {y_predicted[i]}")
        print(f"  Error:     {y_test[i] - y_predicted[i]}")
        print()

    # Summary
    print("="*80)
    print("EXTERNAL DATA TESTING COMPLETE!")
    print("="*80)
    print(f"Test samples: {len(X_test)}")
    print(f"Overall R² Score: {overall_r2:.4f}")
    print(f"Generated plots:")
    print(f"  - {model_name}_test_predictions_vs_actual.png")
    print(f"  - {model_name}_test_time_series.png")

    return overall_r2

if __name__ == "__main__":
    import sys

    # Check if this is being called for external testing
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        if len(sys.argv) == 4:
            # Prediction-only mode: python3 Full_Neural_Network.py test <model_name> <input_file>
            model_name = sys.argv[2]
            input_file = sys.argv[3]
            print("Running in PREDICTION-ONLY mode (no output file provided)")
            predict_only_mode(model_name, input_file)
        elif len(sys.argv) == 5:
            # Comparison mode: python3 Full_Neural_Network.py test <model_name> <input_file> <output_file>
            model_name = sys.argv[2]
            input_file = sys.argv[3]
            output_file = sys.argv[4]
            print("Running in COMPARISON mode (output file provided)")
            test_model_on_external_data(model_name, input_file, output_file)
        else:
            print("Usage:")
            print("  Prediction-only mode: python3 Full_Neural_Network.py test <model_name> <input_file>")
            print("  Comparison mode:      python3 Full_Neural_Network.py test <model_name> <input_file> <output_file>")
            print()
            print("Examples:")
            print("  python3 Full_Neural_Network.py test normalized_model data.csv")
            print("  python3 Full_Neural_Network.py test normalized_model data.csv data_output.csv")
    else:
        main()